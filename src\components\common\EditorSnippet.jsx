import React, { useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/components/prism-markup";
import "prismjs/components/prism-css";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism-dark.css";

const highlightHTML = (code) => {
  return (
    Prism.highlight(code, Prism.languages.markup, "markup")
      // Add custom highlighting for placeholders ${...}
      .replace(
        /(\$\{)([^}]+)(\})/g,
        '<span style="color: #dcdcaa; font-weight: bold;">$1</span><span style="color: #9cdcfe; font-weight: bold;">$2</span><span style="color: #dcdcaa; font-weight: bold;">$3</span>'
      )
  );
};

const highlightCSS = (code) => {
  return Prism.highlight(code, Prism.languages.css, "css");
};

const highlightJS = (code) => {
  return Prism.highlight(code, Prism.languages.javascript, "javascript");
};

const highlight = (code, type) => {
  if (!code) return "";
  switch (type) {
    case "html":
      return highlightHTML(code);
    case "css":
      return highlightCSS(code);
    case "js":
      return highlightJS(code);
    default:
      return highlightHTML(code);
  }
  //   return Prism.highlight(code, Prism.languages.javascript, "javascript");
};

const EditorSnippet = ({
  type = "html",
  placeholder = "Enter your HTML content here...",
  //   formData,
  //   setFormData,
  onValueChange,
  defaultValue = "",
  textareaId = "html",
}) => {
  // Add custom styles to ensure Prism.js tokens are visible
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      .prism-editor-wrapper .token.tag,
      .prism-editor-wrapper .token.attr-name,
      .prism-editor-wrapper .token.attr-value,
      .prism-editor-wrapper .token.punctuation,
      .prism-editor-wrapper .token.string,
      .prism-editor-wrapper .token.property,
      .prism-editor-wrapper .token.selector,
      .prism-editor-wrapper .token.function,
      .prism-editor-wrapper .token.keyword,
      .prism-editor-wrapper .token.comment,
      .prism-editor-wrapper .token.number,
      .prism-editor-wrapper .token.boolean,
      .prism-editor-wrapper .token.operator {
        color: inherit !important;
      }

      /* Ensure Prism dark theme colors are applied */
      .prism-editor-wrapper .token.tag { color: #f92672 !important; }
      .prism-editor-wrapper .token.attr-name { color: #a6e22e !important; }
      .prism-editor-wrapper .token.attr-value { color: #e6db74 !important; }
      .prism-editor-wrapper .token.punctuation { color: #f8f8f2 !important; }
      .prism-editor-wrapper .token.string { color: #e6db74 !important; }
      .prism-editor-wrapper .token.property { color: #66d9ef !important; }
      .prism-editor-wrapper .token.selector { color: #a6e22e !important; }
      .prism-editor-wrapper .token.function { color: #a6e22e !important; }
      .prism-editor-wrapper .token.keyword { color: #f92672 !important; }
      .prism-editor-wrapper .token.comment { color: #75715e !important; }
      .prism-editor-wrapper .token.number { color: #ae81ff !important; }
      .prism-editor-wrapper .token.boolean { color: #ae81ff !important; }
      .prism-editor-wrapper .token.operator { color: #f92672 !important; }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="prism-editor-wrapper">
      <Editor
        // value={formData.js_content}
        defaultValue={defaultValue}
        onValueChange={onValueChange}
        highlight={(code) => highlight(code, type)}
        padding={16}
        style={{
          fontFamily:
            'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
          fontSize: 14,
          lineHeight: 1.6,
          backgroundColor: "#1e1e1e",
          minHeight: "200px",
          outline: "none",
        }}
        placeholder={placeholder}
        textareaId={textareaId}
        className="tw-focus:tw-outline-none"
      />
    </div>
  );
};

export default EditorSnippet;
